'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Clock, 
  FolderOpen, 
  BarChart3, 
  Settings,
  ChevronRight,
  Timer,
  Calendar
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface TimeTrackerLayoutProps {
  children: React.ReactNode
}

const navigation = [
  {
    name: 'Overview',
    href: '/dashboard/time-tracker',
    icon: BarChart3,
    description: 'Time tracking overview and analytics'
  },
  {
    name: 'Timesheets',
    href: '/dashboard/time-tracker/timesheets',
    icon: Clock,
    description: 'Manage and submit timesheets'
  },
  {
    name: 'Projects',
    href: '/dashboard/time-tracker/projects',
    icon: FolderOpen,
    description: 'Manage projects and assignments'
  }
]

export default function TimeTrackerLayout({ children }: TimeTrackerLayoutProps) {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)

  return (
    <div className="flex h-full">
      {/* Sidebar Navigation */}
      <div className={cn(
        "border-r bg-muted/10 transition-all duration-300",
        isCollapsed ? "w-16" : "w-64"
      )}>
        <div className="p-4">
          {/* Header */}
          <div className="flex items-center gap-2 mb-6">
            <div className="flex items-center justify-center w-8 h-8 bg-primary rounded-lg">
              <Timer className="h-4 w-4 text-primary-foreground" />
            </div>
            {!isCollapsed && (
              <div>
                <h2 className="font-semibold text-lg">Time Tracker</h2>
                <p className="text-sm text-muted-foreground">Manage time & projects</p>
              </div>
            )}
          </div>

          {/* Navigation Links */}
          <nav className="space-y-2">
            {navigation.map((item) => {
              const isActive = pathname === item.href
              const Icon = item.icon

              return (
                <Link key={item.name} href={item.href}>
                  <Button
                    variant={isActive ? "secondary" : "ghost"}
                    className={cn(
                      "w-full justify-start gap-3 h-auto p-3",
                      isCollapsed && "px-3 justify-center"
                    )}
                  >
                    <Icon className="h-4 w-4 flex-shrink-0" />
                    {!isCollapsed && (
                      <div className="flex-1 text-left">
                        <div className="font-medium">{item.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {item.description}
                        </div>
                      </div>
                    )}
                    {!isCollapsed && isActive && (
                      <ChevronRight className="h-4 w-4" />
                    )}
                  </Button>
                </Link>
              )
            })}
          </nav>

          {/* Quick Stats */}
          {!isCollapsed && (
            <div className="mt-8 space-y-3">
              <Separator />
              <div className="text-sm font-medium text-muted-foreground">
                Quick Stats
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">This Week</span>
                  <Badge variant="outline">40h</Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Active Projects</span>
                  <Badge variant="outline">5</Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Pending</span>
                  <Badge variant="outline">2</Badge>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Collapse Toggle */}
        <div className="absolute bottom-4 left-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="h-8 w-8 p-0"
          >
            <ChevronRight 
              className={cn(
                "h-4 w-4 transition-transform",
                isCollapsed ? "rotate-0" : "rotate-180"
              )} 
            />
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="p-6">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
            <Link href="/dashboard" className="hover:text-foreground">
              Dashboard
            </Link>
            <ChevronRight className="h-4 w-4" />
            <Link href="/dashboard/time-tracker" className="hover:text-foreground">
              Time Tracker
            </Link>
            {pathname !== '/dashboard/time-tracker' && (
              <>
                <ChevronRight className="h-4 w-4" />
                <span className="text-foreground">
                  {pathname.includes('/timesheets') ? 'Timesheets' : 
                   pathname.includes('/projects') ? 'Projects' : 'Overview'}
                </span>
              </>
            )}
          </div>

          {/* Page Content */}
          {children}
        </div>
      </div>
    </div>
  )
}
